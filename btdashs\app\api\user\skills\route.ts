// app/api/user/skills/route.ts

import { NextResponse } from "next/server";
import { auth0 } from "../../../../lib/auth0";

export const GET = async function getUserSkills(request: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const response = await fetch(`${process.env.API_BASE_URL}/user/skills`, {
			headers: {
				Authorization: `Bearer ${accessToken}`,
			},
		});

		if (!response.ok) {
			const error = await response.json();
			return NextResponse.json({ error: error.message }, { status: response.status });
		}

		const skills = await response.json();
		return NextResponse.json(skills);
	} catch (error) {
		console.error("Error fetching skills:", error);
		return NextResponse.json({ error: "Server error" }, { status: 500 });
	}
};

// Add a new skill with level
export const POST = async function addUserSkill(request: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const { skill_id, level } = await request.json();

		if (!skill_id || !level) {
			return NextResponse.json({ error: "Skill ID and Level are required" }, { status: 400 });
		}

		const response = await fetch(`${process.env.API_BASE_URL}/user/skills`, {
			method: "POST",
			headers: {
				Authorization: `Bearer ${accessToken}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify({
				skill_id,
				level,
				endorsements: 0,
			}),
		});

		if (!response.ok) {
			const error = await response.json();
			return NextResponse.json({ error: error.message }, { status: response.status });
		}

		const skill = await response.json();
		return NextResponse.json({ data: skill }, { status: 201 });
	} catch (error) {
		console.error("Error adding skill:", error);
		return NextResponse.json({ error: "Server error" }, { status: 500 });
	}
};
